import React, { useState } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useSupabaseWorkspace } from '../contexts/SupabaseWorkspaceContext';
import { useAuth } from '../contexts/SupabaseAuthContext';
import { useTheme } from '../contexts/ThemeContext';
import { useTask } from '../contexts/TaskContext';
import {
  Home,
  Globe,
  CheckSquare,
  Users,
  BarChart3,
  Settings,
  Plus,
  ChevronDown,
  ChevronRight,
  ChevronLeft,
  Calendar,
  Clock,
  Target,
  TrendingUp,
  Zap,
  Star,
  Search,
  Bell,
  Sun,
  Moon,
  Monitor,
  LogOut,
  Menu,
  X
} from 'lucide-react';

interface ModernSidebarProps {
  isCollapsed: boolean;
  onToggleCollapse: () => void;
}

const ModernSidebar: React.FC<ModernSidebarProps> = ({ isCollapsed, onToggleCollapse }) => {
  const location = useLocation();
  const navigate = useNavigate();
  const { user, signOut } = useAuth();
  const { theme, setTheme, actualTheme } = useTheme();
  const { currentWorkspace, workspaceMembers } = useSupabaseWorkspace();
  const { state } = useTask();
  const [projectsExpanded, setProjectsExpanded] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');

  const navigationItems = [
    {
      name: 'Dashboard',
      href: '/',
      icon: Home,
      current: location.pathname === '/',
      badge: null
    },
    {
      name: 'My Websites',
      href: '/websites',
      icon: Globe,
      current: location.pathname === '/websites',
      badge: state.pages.length
    },
    {
      name: 'Tasks',
      href: '/tasker',
      icon: CheckSquare,
      current: location.pathname === '/tasker',
      badge: state.pages.reduce((total, page) => total + page.tasks.length, 0) + state.unassignedTasks.length
    },
    {
      name: 'Team',
      href: '/team',
      icon: Users,
      current: location.pathname === '/team',
      badge: workspaceMembers.length
    },
    {
      name: 'Analytics',
      href: '/analytics',
      icon: BarChart3,
      current: location.pathname === '/analytics',
      badge: null
    },
    {
      name: 'Calendar',
      href: '/calendar',
      icon: Calendar,
      current: location.pathname === '/calendar',
      badge: null
    },
  ];

  const handleSignOut = async () => {
    try {
      await signOut();
      navigate('/login');
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  const getThemeIcon = () => {
    switch (theme) {
      case 'light': return <Sun className="h-4 w-4" />;
      case 'dark': return <Moon className="h-4 w-4" />;
      default: return <Monitor className="h-4 w-4" />;
    }
  };

  const quickActions = [
    { name: 'New Task', icon: Plus, action: () => navigate('/tasker'), color: 'bg-blue-500' },
    { name: 'Add Website', icon: Globe, action: () => navigate('/add-page'), color: 'bg-green-500' },
    { name: 'Invite Member', icon: Users, action: () => navigate('/team'), color: 'bg-purple-500' },
  ];

  // Calculate task statistics
  const totalTasks = state.pages.reduce((total, page) => total + page.tasks.length, 0) + state.unassignedTasks.length;
  const completedTasks = state.pages.reduce((total, page) =>
    total + page.tasks.filter(task => task.status === 'done').length, 0
  ) + state.unassignedTasks.filter(task => task.status === 'done').length;
  const progressPercentage = totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0;

  return (
    <aside className={`${isCollapsed ? 'w-16' : 'w-80'} h-screen bg-card border-r border-border flex flex-col transition-all duration-300`}>
      {/* Header with Logo and Collapse Toggle */}
      <div className="p-4 border-b border-border">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-orange-500 to-orange-600 flex items-center justify-center">
              <CheckSquare className="h-5 w-5 text-white" />
            </div>
            {!isCollapsed && (
              <div className="flex-1 min-w-0">
                <h2 className="font-bold text-lg bg-gradient-to-r from-orange-500 to-orange-600 bg-clip-text text-transparent">
                  TaskHub
                </h2>
                <p className="text-xs text-muted-foreground">
                  {currentWorkspace?.name || 'My Workspace'}
                </p>
              </div>
            )}
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={onToggleCollapse}
            className="h-8 w-8 p-0"
          >
            {isCollapsed ? <ChevronRight className="h-4 w-4" /> : <ChevronLeft className="h-4 w-4" />}
          </Button>
        </div>
      </div>

      {/* Search Bar */}
      {!isCollapsed && (
        <div className="p-4 border-b border-border">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              type="text"
              placeholder="Search tasks, websites..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 bg-muted/50 border-0 focus:bg-background"
            />
          </div>
        </div>
      )}

      {/* Navigation */}
      <nav className="flex-1 p-4 space-y-1 overflow-y-auto">
        {navigationItems.map((item) => (
          <Link
            key={item.name}
            to={item.href}
            className={`flex items-center ${isCollapsed ? 'justify-center px-2' : 'justify-between px-3'} py-2.5 rounded-lg text-sm font-medium transition-all duration-200 group ${
              item.current
                ? 'bg-primary text-primary-foreground shadow-sm'
                : 'text-muted-foreground hover:text-foreground hover:bg-accent'
            }`}
            title={isCollapsed ? item.name : undefined}
          >
            <div className={`flex items-center ${isCollapsed ? 'justify-center' : 'gap-3'}`}>
              <item.icon className={`h-4 w-4 ${item.current ? 'text-primary-foreground' : 'text-muted-foreground group-hover:text-foreground'}`} />
              {!isCollapsed && <span>{item.name}</span>}
            </div>
            {!isCollapsed && item.badge !== null && item.badge > 0 && (
              <Badge variant={item.current ? "secondary" : "outline"} className="text-xs">
                {item.badge}
              </Badge>
            )}
          </Link>
        ))}

        {/* Projects Section */}
        {!isCollapsed && (
          <div className="pt-6">
            <button
              onClick={() => setProjectsExpanded(!projectsExpanded)}
              className="flex items-center justify-between w-full px-3 py-2 text-sm font-medium text-muted-foreground hover:text-foreground transition-colors"
            >
              <span>Projects</span>
              {projectsExpanded ? (
                <ChevronDown className="h-4 w-4" />
              ) : (
                <ChevronRight className="h-4 w-4" />
              )}
            </button>

            {projectsExpanded && (
              <div className="mt-2 space-y-1">
                {state.pages.slice(0, 5).map((page) => (
                  <Link
                    key={page.id}
                    to={`/tasker?page=${page.id}`}
                    className="flex items-center justify-between px-6 py-2 rounded-lg text-sm text-muted-foreground hover:text-foreground hover:bg-accent transition-colors group"
                  >
                    <div className="flex items-center gap-2 min-w-0">
                      <div className="w-2 h-2 rounded-full bg-orange-500 flex-shrink-0" />
                      <span className="truncate">{page.title}</span>
                    </div>
                    <Badge variant="outline" className="text-xs">
                      {page.tasks.length}
                    </Badge>
                  </Link>
                ))}

                {state.pages.length === 0 && (
                  <div className="px-6 py-2 text-xs text-muted-foreground">
                    No projects yet
                  </div>
                )}

                <Link
                  to="/add-page"
                  className="flex items-center gap-2 px-6 py-2 rounded-lg text-sm text-muted-foreground hover:text-foreground hover:bg-accent transition-colors"
                >
                  <Plus className="h-3 w-3" />
                  <span>Add Project</span>
                </Link>
              </div>
            )}
          </div>
        )}
      </nav>

      {/* Progress Card */}
      {!isCollapsed && (
        <div className="p-4 border-t border-border">
          <Card className="bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-950 dark:to-orange-900 border-orange-200 dark:border-orange-800">
            <CardContent className="p-4">
              <div className="flex items-center gap-2 mb-3">
                <Target className="h-4 w-4 text-orange-600 dark:text-orange-400" />
                <span className="text-sm font-medium text-orange-900 dark:text-orange-100">
                  Today's Progress
                </span>
              </div>

              <div className="space-y-2">
                <div className="flex justify-between text-xs text-orange-700 dark:text-orange-300">
                  <span>{completedTasks} completed</span>
                  <span>{totalTasks} total</span>
                </div>
                <Progress
                  value={progressPercentage}
                  className="h-2 bg-orange-200 dark:bg-orange-800"
                />
                <div className="text-xs text-orange-600 dark:text-orange-400">
                  {Math.round(progressPercentage)}% complete
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Quick Actions */}
      <div className="p-4 border-t border-border">
        {!isCollapsed ? (
          <div className="space-y-2">
            <h3 className="text-xs font-medium text-muted-foreground uppercase tracking-wider">
              Quick Actions
            </h3>
            <div className="grid grid-cols-3 gap-2">
              {quickActions.map((action) => (
                <Button
                  key={action.name}
                  variant="ghost"
                  size="sm"
                  className="h-auto p-2 flex flex-col items-center gap-1 hover:bg-accent"
                  onClick={action.action}
                >
                  <div className={`w-6 h-6 rounded-md ${action.color} flex items-center justify-center`}>
                    <action.icon className="h-3 w-3 text-white" />
                  </div>
                  <span className="text-xs">{action.name.split(' ')[0]}</span>
                </Button>
              ))}
            </div>
          </div>
        ) : (
          <div className="space-y-2">
            {quickActions.map((action) => (
              <Button
                key={action.name}
                variant="ghost"
                size="sm"
                className="w-full h-10 p-0 flex items-center justify-center hover:bg-accent"
                onClick={action.action}
                title={action.name}
              >
                <action.icon className="h-4 w-4" />
              </Button>
            ))}
          </div>
        )}
      </div>

      {/* Theme Toggle and User Menu */}
      <div className="p-4 border-t border-border space-y-2">
        {/* Theme Toggle */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              className={`${isCollapsed ? 'w-full justify-center' : 'w-full justify-start'} h-10`}
              title={isCollapsed ? 'Theme' : undefined}
            >
              {getThemeIcon()}
              {!isCollapsed && <span className="ml-2">Theme</span>}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align={isCollapsed ? "center" : "end"}>
            <DropdownMenuLabel>Theme</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={() => setTheme('light')}>
              <Sun className="h-4 w-4 mr-2" />
              Light
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => setTheme('dark')}>
              <Moon className="h-4 w-4 mr-2" />
              Dark
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => setTheme('system')}>
              <Monitor className="h-4 w-4 mr-2" />
              System
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Notifications */}
        <Button
          variant="ghost"
          size="sm"
          className={`${isCollapsed ? 'w-full justify-center' : 'w-full justify-start'} h-10 relative`}
          title={isCollapsed ? 'Notifications' : undefined}
        >
          <Bell className="h-4 w-4" />
          {!isCollapsed && <span className="ml-2">Notifications</span>}
          <Badge className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0 text-xs">
            3
          </Badge>
        </Button>

        {/* User Menu */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              className={`${isCollapsed ? 'w-full h-10 p-0' : 'w-full justify-start h-12 px-3'}`}
            >
              <div className="flex items-center gap-2">
                <Avatar className="h-8 w-8">
                  <AvatarImage src={user?.user_metadata?.avatar_url} alt={user?.email} />
                  <AvatarFallback className="bg-gradient-to-br from-orange-500 to-orange-600 text-white">
                    {user?.email?.charAt(0).toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                {!isCollapsed && (
                  <div className="flex flex-col items-start">
                    <span className="text-sm font-medium">
                      {user?.user_metadata?.full_name || user?.email?.split('@')[0] || 'User'}
                    </span>
                    <span className="text-xs text-muted-foreground">
                      {user?.email}
                    </span>
                  </div>
                )}
              </div>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="w-56" align={isCollapsed ? "center" : "end"} forceMount>
            <DropdownMenuLabel className="font-normal">
              <div className="flex flex-col space-y-1">
                <p className="text-sm font-medium leading-none">
                  {user?.user_metadata?.full_name || 'User'}
                </p>
                <p className="text-xs leading-none text-muted-foreground">
                  {user?.email}
                </p>
              </div>
            </DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={() => navigate('/profile')}>
              <Users className="h-4 w-4 mr-2" />
              Profile
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => navigate('/settings')}>
              <Settings className="h-4 w-4 mr-2" />
              Settings
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={handleSignOut}>
              <LogOut className="h-4 w-4 mr-2" />
              Sign out
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </aside>
  );
};

export default ModernSidebar;
