import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useTask } from '../contexts/TaskContext';
import { useAuth } from '../contexts/SupabaseAuthContext';
import { useSupabaseWorkspace } from '../contexts/SupabaseWorkspaceContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  Globe,
  Plus,
  ArrowRight,
  CheckCircle2,
  Zap,
  Target,
  Users,
  Crown,
  BarChart3,
  Clock,
  Calendar,
  TrendingUp,
  Activity,
  ExternalLink,
  CheckSquare
} from 'lucide-react';
import ModernLayout from '../components/ModernLayout';
import InvitationManager from '../components/InvitationManager';

const Home: React.FC = () => {
  const { state } = useTask();
  const { user } = useAuth();
  const { currentWorkspace, workspaceMembers } = useSupabaseWorkspace();
  const navigate = useNavigate();

  // Calculate task statistics
  const totalTasks = state.pages.reduce((total, page) => total + page.tasks.length, 0) + state.unassignedTasks.length;
  const completedTasks = state.pages.reduce((total, page) =>
    total + page.tasks.filter(task => task.status === 'done').length, 0
  ) + state.unassignedTasks.filter(task => task.status === 'done').length;
  const inProgressTasks = state.pages.reduce((total, page) =>
    total + page.tasks.filter(task => task.status === 'in-progress').length, 0
  ) + state.unassignedTasks.filter(task => task.status === 'in-progress').length;
  const todoTasks = totalTasks - completedTasks - inProgressTasks;
  const progressPercentage = totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0;

  // Get recent tasks (last 5)
  const recentTasks = state.pages
    .flatMap(page => page.tasks.map(task => ({ ...task, pageName: page.title })))
    .concat(state.unassignedTasks.map(task => ({ ...task, pageName: 'Unassigned' })))
    .sort((a, b) => new Date(b.createdAt || '').getTime() - new Date(a.createdAt || '').getTime())
    .slice(0, 5);

  // Get recent websites (last 3)
  const recentWebsites = state.pages.slice(0, 3);

  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Good morning';
    if (hour < 18) return 'Good afternoon';
    return 'Good evening';
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'done': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'in-progress': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      default: return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
    }
  };



  return (
    <ModernLayout>
      <div className="space-y-6">
        {/* Welcome Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-foreground">
              {getGreeting()}, {user?.user_metadata?.full_name || user?.email?.split('@')[0] || 'there'}! 👋
            </h1>
            <p className="text-muted-foreground mt-1">
              Let's organize your Daily Tasks
            </p>
          </div>
          <div className="flex items-center gap-3">
            <Button onClick={() => navigate('/add-page')} className="btn-orange">
              <Plus className="h-4 w-4 mr-2" />
              New Website
            </Button>
            <Button onClick={() => navigate('/tasker')} variant="outline">
              <CheckSquare className="h-4 w-4 mr-2" />
              New Task
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="card-modern card-hover">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Tasks</CardTitle>
              <CheckSquare className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-primary">{totalTasks}</div>
              <p className="text-xs text-muted-foreground">
                {completedTasks} completed
              </p>
            </CardContent>
          </Card>

          <Card className="card-modern card-hover">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Websites</CardTitle>
              <Globe className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-primary">{state.pages.length}</div>
              <p className="text-xs text-muted-foreground">
                Active projects
              </p>
            </CardContent>
          </Card>

          <Card className="card-modern card-hover">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Team Members</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-primary">{workspaceMembers.length}</div>
              <p className="text-xs text-muted-foreground">
                In workspace
              </p>
            </CardContent>
          </Card>

          <Card className="card-modern card-hover">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Progress</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-primary">{Math.round(progressPercentage)}%</div>
              <Progress value={progressPercentage} className="mt-2" />
            </CardContent>
          </Card>
        </div>
        {/* Main Dashboard Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Recent Tasks */}
          <div className="lg:col-span-2">
            <Card className="card-modern">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center gap-2">
                    <Activity className="h-5 w-5 text-primary" />
                    Recent Tasks
                  </CardTitle>
                  <Button variant="ghost" size="sm" onClick={() => navigate('/tasker')}>
                    View All
                    <ArrowRight className="h-4 w-4 ml-1" />
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                {recentTasks.length === 0 ? (
                  <div className="text-center py-8">
                    <CheckSquare className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-medium mb-2">No tasks yet</h3>
                    <p className="text-muted-foreground mb-4">Create your first task to get started</p>
                    <Button onClick={() => navigate('/tasker')} className="btn-orange">
                      <Plus className="h-4 w-4 mr-2" />
                      Create Task
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {recentTasks.map((task) => (
                      <div key={task.id} className="flex items-center justify-between p-3 rounded-lg border hover:bg-accent transition-colors">
                        <div className="flex items-center gap-3">
                          <div className={`w-2 h-2 rounded-full ${
                            task.status === 'done' ? 'bg-green-500' :
                            task.status === 'in-progress' ? 'bg-yellow-500' : 'bg-blue-500'
                          }`} />
                          <div>
                            <p className="font-medium">{task.title}</p>
                            <p className="text-sm text-muted-foreground">{task.pageName}</p>
                          </div>
                        </div>
                        <Badge className={getStatusColor(task.status)}>
                          {task.status}
                        </Badge>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Right Sidebar */}
          <div className="space-y-6">
            {/* Task Progress */}
            <Card className="card-modern">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Target className="h-5 w-5 text-primary" />
                  Task Progress
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between text-sm">
                    <span>Completed</span>
                    <span>{completedTasks}/{totalTasks}</span>
                  </div>
                  <Progress value={progressPercentage} className="h-2" />

                  <div className="grid grid-cols-3 gap-2 text-center">
                    <div className="p-2 rounded-lg bg-blue-50 dark:bg-blue-950">
                      <div className="text-lg font-bold text-blue-600">{todoTasks}</div>
                      <div className="text-xs text-blue-600">To Do</div>
                    </div>
                    <div className="p-2 rounded-lg bg-yellow-50 dark:bg-yellow-950">
                      <div className="text-lg font-bold text-yellow-600">{inProgressTasks}</div>
                      <div className="text-xs text-yellow-600">In Progress</div>
                    </div>
                    <div className="p-2 rounded-lg bg-green-50 dark:bg-green-950">
                      <div className="text-lg font-bold text-green-600">{completedTasks}</div>
                      <div className="text-xs text-green-600">Completed</div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Recent Websites */}
            <Card className="card-modern">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center gap-2">
                    <Globe className="h-5 w-5 text-primary" />
                    Recent Websites
                  </CardTitle>
                  <Button variant="ghost" size="sm" onClick={() => navigate('/websites')}>
                    View All
                    <ArrowRight className="h-4 w-4 ml-1" />
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                {recentWebsites.length === 0 ? (
                  <div className="text-center py-4">
                    <Globe className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                    <p className="text-sm text-muted-foreground mb-3">No websites yet</p>
                    <Button size="sm" onClick={() => navigate('/add-page')} className="btn-orange">
                      <Plus className="h-3 w-3 mr-1" />
                      Add Website
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {recentWebsites.map((website) => (
                      <div key={website.id} className="flex items-center justify-between p-2 rounded-lg hover:bg-accent transition-colors">
                        <div className="flex items-center gap-2 min-w-0">
                          <div className="w-2 h-2 rounded-full bg-primary flex-shrink-0" />
                          <div className="min-w-0">
                            <p className="font-medium text-sm truncate">{website.title}</p>
                            <p className="text-xs text-muted-foreground truncate">{website.url}</p>
                          </div>
                        </div>
                        <div className="flex items-center gap-1">
                          <Badge variant="outline" className="text-xs">
                            {website.tasks.length}
                          </Badge>
                          <Button variant="ghost" size="sm" onClick={() => window.open(website.url, '_blank')}>
                            <ExternalLink className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Team Members */}
            <Card className="card-modern">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5 text-primary" />
                  Team Members
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {workspaceMembers.slice(0, 3).map((member) => (
                    <div key={member.userId} className="flex items-center gap-3">
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={member.photoURL} />
                        <AvatarFallback className="bg-gradient-to-br from-orange-500 to-orange-600 text-white text-xs">
                          {member.displayName.charAt(0).toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium truncate">{member.displayName}</p>
                        <p className="text-xs text-muted-foreground">{member.role}</p>
                      </div>
                      <div className="w-2 h-2 rounded-full bg-green-500" />
                    </div>
                  ))}
                  {workspaceMembers.length > 3 && (
                    <p className="text-xs text-muted-foreground text-center pt-2">
                      +{workspaceMembers.length - 3} more members
                    </p>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Invitation Manager */}
        <div className="mt-8">
          <InvitationManager />
        </div>
      </div>
    </ModernLayout>
  );
};

export default Home;
