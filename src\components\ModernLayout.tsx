import React from 'react';
import ModernHeader from './ModernHeader';
import ModernSidebar from './ModernSidebar';

interface ModernLayoutProps {
  children: React.ReactNode;
  showSidebar?: boolean;
}

const ModernLayout: React.FC<ModernLayoutProps> = ({ 
  children, 
  showSidebar = true 
}) => {
  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <ModernHeader />
      
      <div className="flex">
        {/* Sidebar */}
        {showSidebar && <ModernSidebar />}
        
        {/* Main Content */}
        <main className={`flex-1 ${showSidebar ? '' : 'w-full'}`}>
          <div className="p-6">
            {children}
          </div>
        </main>
      </div>
    </div>
  );
};

export default ModernLayout;
