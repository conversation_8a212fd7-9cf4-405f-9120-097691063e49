import React, { useState, useEffect } from 'react';
import { useTask } from '../contexts/TaskContext';
import { useAuth } from '../contexts/SupabaseAuthContext';
import { handleDragOver, handleDragEnter, handleDragLeave, handleDrop } from '../utils/dragDrop';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Search,
  Plus,
  Inbox,
  BarChart3,
  CheckCircle2,
  Globe,
  Timer,
  MessageCircle,
  GitBranch,
  Layers,
  Settings,
  PlayCircle,
  PauseCircle,
  Clock
} from 'lucide-react';
import TaskCard from '../components/TaskCard';
import PageCard from '../components/PageCard';
import AddTaskModal from '../components/AddTaskModal';
import AddPageModal from '../components/AddPageModal';
import TaskDependencyManager from '../components/TaskDependencyManager';
import SubtaskManager from '../components/SubtaskManager';
import TaskComments from '../components/TaskComments';
import TimeTracker from '../components/TimeTracker';
import ModernLayout from '../components/ModernLayout';
import { Link } from 'react-router-dom';
import { supabase } from '../lib/supabase';

const Tasker: React.FC = () => {
  const { state, moveTask, searchTasks, updateTask } = useTask();
  const { user } = useAuth();
  const [searchQuery, setSearchQuery] = useState('');
  const [showAddPageModal, setShowAddPageModal] = useState(false);
  const [showAddTaskModal, setShowAddTaskModal] = useState(false);
  const [selectedTask, setSelectedTask] = useState<any>(null);
  const [showTaskDetails, setShowTaskDetails] = useState(false);
  const [advancedStats, setAdvancedStats] = useState({
    totalTimeTracked: 0,
    activeTimers: 0,
    totalComments: 0,
    totalDependencies: 0,
    totalSubtasks: 0,
    databaseSetup: false
  });

  // Load advanced features stats
  useEffect(() => {
    loadAdvancedStats();
  }, [user]);

  const loadAdvancedStats = async () => {
    if (!user) return;

    try {
      // Check if advanced features tables exist
      const { data: tables } = await supabase
        .from('information_schema.tables')
        .select('table_name')
        .eq('table_schema', 'public')
        .in('table_name', ['task_dependencies', 'subtasks', 'task_comments', 'task_time_entries']);

      const databaseSetup = tables?.length === 4;

      if (!databaseSetup) {
        setAdvancedStats(prev => ({ ...prev, databaseSetup: false }));
        return;
      }

      // Load stats
      const [timeEntries, comments, dependencies, subtasks] = await Promise.all([
        supabase.from('task_time_entries').select('duration_minutes, end_time').eq('user_id', user.id),
        supabase.from('task_comments').select('*', { count: 'exact', head: true }).eq('user_id', user.id),
        supabase.from('task_dependencies').select('*', { count: 'exact', head: true }).eq('created_by', user.id),
        supabase.from('subtasks').select('*').eq('created_by', user.id)
      ]);

      const totalTimeTracked = timeEntries.data?.reduce((sum, entry) => sum + (entry.duration_minutes || 0), 0) || 0;
      const activeTimers = timeEntries.data?.filter(entry => !entry.end_time).length || 0;

      setAdvancedStats({
        totalTimeTracked,
        activeTimers,
        totalComments: comments.count || 0,
        totalDependencies: dependencies.count || 0,
        totalSubtasks: subtasks.data?.length || 0,
        databaseSetup: true
      });

    } catch (error) {
      console.error('Error loading advanced stats:', error);
    }
  };

  const openTaskDetails = (task: any) => {
    setSelectedTask(task);
    setShowTaskDetails(true);
  };

  if (state.pages.length === 0) {
    return (
      <ModernLayout>
        <div className="max-w-4xl mx-auto">
          <Card className="card-modern p-8 sm:p-12 text-center">
            <div className="max-w-md mx-auto space-y-6">
              <div className="w-20 h-20 bg-gradient-to-r from-orange-500 to-orange-600 rounded-2xl mx-auto flex items-center justify-center">
                <Globe className="w-10 h-10 text-white" />
              </div>
              <h2 className="text-2xl sm:text-3xl font-bold text-foreground">
                No Websites Yet
              </h2>
              <p className="text-muted-foreground text-base">
                You need to add at least one website or project before you can start managing tasks.
                Each website will have its own dedicated task board.
              </p>
              <div className="space-y-3">
                <Link to="/websites">
                  <Button
                    size="lg"
                    className="btn-orange w-full"
                  >
                    <Plus className="w-5 h-5 mr-2" />
                    Add Your First Website
                  </Button>
                </Link>
                <p className="text-sm text-muted-foreground">
                  Once you add a website, you'll be able to create and manage tasks for it.
                </p>
              </div>
            </div>
          </Card>
        </div>
      </ModernLayout>
    );
  }

  const filteredTasks = searchQuery.trim() 
    ? searchTasks(searchQuery)
    : state.unassignedTasks;

  const handleUnassignedDrop = (event: React.DragEvent) => {
    const dragData = handleDrop(event);
    if (dragData && dragData.type === 'task') {
      moveTask(dragData.taskId, undefined);
    }
  };

  const totalTasks = state.unassignedTasks.length + 
    state.pages.reduce((total, page) => total + page.tasks.length, 0);

  const completedTasks = state.unassignedTasks.filter(task => task.status === 'done').length +
    state.pages.reduce((total, page) => 
      total + page.tasks.filter(task => task.status === 'done').length, 0
    );

  const completionRate = totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0;

  return (
    <ModernLayout>
      <div className="space-y-6">
          {/* Header */}
          <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-4">
            <div className="space-y-2">
              <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-foreground">
                Task Dashboard
              </h1>
              <p className="text-muted-foreground text-sm sm:text-base">
                Organize your work with drag-and-drop simplicity
              </p>
            </div>
            <div className="flex gap-2">
              <Link to="/websites">
                <Button variant="outline" size="lg">
                  <Globe className="w-4 h-4 mr-2" />
                  Manage Websites
                </Button>
              </Link>
              <Button
                onClick={() => setShowAddPageModal(true)}
                className="btn-orange"
                size="lg"
              >
                <Plus className="w-4 h-4 mr-2" />
                Add Website
              </Button>
            </div>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-3 sm:gap-4">
            <Card className="card-modern p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                  <BarChart3 className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                  <p className="text-xs sm:text-sm text-muted-foreground">Total Tasks</p>
                  <p className="text-lg sm:text-xl font-bold text-foreground">{totalTasks}</p>
                </div>
              </div>
            </Card>

            <Card className="card-modern p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
                  <CheckCircle2 className="w-4 h-4 text-green-600 dark:text-green-400" />
                </div>
                <div>
                  <p className="text-xs sm:text-sm text-muted-foreground">Completed</p>
                  <p className="text-lg sm:text-xl font-bold text-green-600 dark:text-green-400">{completedTasks}</p>
                </div>
              </div>
            </Card>

            <Card className="card-modern p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-orange-100 dark:bg-orange-900 rounded-lg">
                  <Timer className="w-4 h-4 text-orange-600 dark:text-orange-400" />
                </div>
                <div>
                  <p className="text-xs sm:text-sm text-muted-foreground">Time Tracked</p>
                  <p className="text-lg sm:text-xl font-bold text-orange-600 dark:text-orange-400">
                    {Math.floor(advancedStats.totalTimeTracked / 60)}h
                  </p>
                </div>
              </div>
            </Card>

            <Card className="card-modern p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-purple-100 dark:bg-purple-900 rounded-lg">
                  <MessageCircle className="w-4 h-4 text-purple-600 dark:text-purple-400" />
                </div>
                <div>
                  <p className="text-xs sm:text-sm text-muted-foreground">Comments</p>
                  <p className="text-lg sm:text-xl font-bold text-purple-600 dark:text-purple-400">{advancedStats.totalComments}</p>
                </div>
              </div>
            </Card>

            <Card className="card-modern p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-indigo-100 dark:bg-indigo-900 rounded-lg">
                  <GitBranch className="w-4 h-4 text-indigo-600 dark:text-indigo-400" />
                </div>
                <div>
                  <p className="text-xs sm:text-sm text-muted-foreground">Dependencies</p>
                  <p className="text-lg sm:text-xl font-bold text-indigo-600 dark:text-indigo-400">{advancedStats.totalDependencies}</p>
                </div>
              </div>
            </Card>

            <Card className="card-modern p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-teal-100 dark:bg-teal-900 rounded-lg">
                  <Layers className="w-4 h-4 text-teal-600 dark:text-teal-400" />
                </div>
                <div>
                  <p className="text-xs sm:text-sm text-muted-foreground">Subtasks</p>
                  <p className="text-lg sm:text-xl font-bold text-teal-600 dark:text-teal-400">{advancedStats.totalSubtasks}</p>
                </div>
              </div>
            </Card>
          </div>

            <Card className="card-modern p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-orange-100 dark:bg-orange-900 rounded-lg">
                  <div className="w-4 h-4 text-orange-600 dark:text-orange-400 font-bold text-xs flex items-center justify-center">
                    %
                  </div>
                </div>
                <div>
                  <p className="text-xs sm:text-sm text-muted-foreground">Progress</p>
                  <p className="text-lg sm:text-xl font-bold text-orange-600 dark:text-orange-400">{completionRate}%</p>
                  </div>
                </div>
              </Card>
            </div>

          <div className="grid lg:grid-cols-4 gap-4 lg:gap-6">
            {/* Sidebar - Unassigned Tasks */}
            <div className="lg:col-span-1 order-2 lg:order-1">
              <Card className="card-modern sticky top-4">
                <CardHeader className="pb-3">
                  <div className="flex items-center gap-2 mb-3">
                    <div className="p-2 bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg">
                      <Inbox className="w-4 h-4 text-white" />
                    </div>
                    <div>
                      <h2 className="font-semibold text-base sm:text-lg text-foreground">Tasks to Assign</h2>
                      <p className="text-xs text-muted-foreground">{filteredTasks.length} tasks</p>
                    </div>
                  </div>
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                    <Input
                      placeholder="Search tasks..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </CardHeader>
              
              <CardContent 
                className="max-h-[50vh] lg:max-h-96 overflow-y-auto space-y-1"
                onDragOver={handleDragOver}
                onDragEnter={handleDragEnter}
                onDragLeave={handleDragLeave}
                onDrop={handleUnassignedDrop}
              >
                {/* Add Task Button */}
                <Button
                  onClick={() => setShowAddTaskModal(true)}
                  className="w-full justify-start text-left btn-orange mb-4"
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Add New Task
                </Button>

                {filteredTasks.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    {searchQuery ? (
                      <div className="space-y-2">
                        <Search className="w-8 h-8 mx-auto opacity-50" />
                        <p className="text-sm">No tasks found for "{searchQuery}"</p>
                      </div>
                    ) : (
                      <div className="space-y-2">
                        <Inbox className="w-8 h-8 mx-auto opacity-50" />
                        <p className="text-sm">No unassigned tasks</p>
                        <p className="text-xs">Click "Add New Task" to get started</p>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="space-y-2">
                    {filteredTasks.map((task, index) => (
                      <TaskCard
                        key={task.id}
                        task={task}
                        index={index}
                        showFullDetails={true}
                        onTaskClick={openTaskDetails}
                      />
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Main Content - Pages */}
          <div className="lg:col-span-3 order-1 lg:order-2">
            {state.pages.length === 0 ? (
              <Card className="card-modern p-8 sm:p-12 text-center">
                <div className="max-w-md mx-auto space-y-4">
                  <div className="w-16 h-16 bg-gradient-to-r from-orange-500 to-orange-600 rounded-2xl mx-auto flex items-center justify-center">
                    <Plus className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-xl sm:text-2xl font-semibold text-foreground">
                    No Pages Yet
                  </h3>
                  <p className="text-muted-foreground text-sm sm:text-base">
                    Create your first page to start organizing your tasks.
                    Pages help you group related tasks together by project or category.
                  </p>
                  <Button
                    onClick={() => setShowAddPageModal(true)}
                    className="btn-orange"
                    size="lg"
                  >
                    <Plus className="w-4 h-4 mr-2" />
                    Create Your First Page
                  </Button>
                </div>
              </Card>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4 sm:gap-6">
                {state.pages.map((page) => (
                  <div key={page.id} className="animate-fade-in">
                    <PageCard page={page} onTaskClick={openTaskDetails} />
                  </div>
                ))}
              </div>
            )}
        </div>
      </div>
      </div>

      <AddPageModal
        isOpen={showAddPageModal}
        onClose={() => setShowAddPageModal(false)}
      />

      <AddTaskModal
        isOpen={showAddTaskModal}
        onClose={() => setShowAddTaskModal(false)}
      />

      {/* Advanced Task Details Dialog */}
      <Dialog open={showTaskDetails} onOpenChange={setShowTaskDetails}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <CheckCircle2 className="h-5 w-5 text-primary" />
              {selectedTask?.title || 'Task Details'}
            </DialogTitle>
            <DialogDescription>
              Advanced task management with dependencies, subtasks, time tracking, and collaboration
            </DialogDescription>
          </DialogHeader>

          {selectedTask && advancedStats.databaseSetup && (
            <Tabs defaultValue="overview" className="w-full">
              <TabsList className="grid w-full grid-cols-5">
                <TabsTrigger value="overview">Overview</TabsTrigger>
                <TabsTrigger value="subtasks">Subtasks</TabsTrigger>
                <TabsTrigger value="dependencies">Dependencies</TabsTrigger>
                <TabsTrigger value="time">Time Tracking</TabsTrigger>
                <TabsTrigger value="comments">Comments</TabsTrigger>
              </TabsList>

              <TabsContent value="overview" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      <span>Task Information</span>
                      <Badge variant={
                        selectedTask.status === 'done' ? 'default' :
                        selectedTask.status === 'in-progress' ? 'secondary' : 'outline'
                      }>
                        {selectedTask.status}
                      </Badge>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div>
                        <h4 className="font-medium">{selectedTask.title}</h4>
                        {selectedTask.description && (
                          <p className="text-sm text-muted-foreground mt-1">{selectedTask.description}</p>
                        )}
                      </div>

                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <span className="text-muted-foreground">Priority:</span>
                          <span className="ml-2 font-medium">{selectedTask.priority || 'Medium'}</span>
                        </div>
                        <div>
                          <span className="text-muted-foreground">Created:</span>
                          <span className="ml-2 font-medium">
                            {selectedTask.createdAt ? new Date(selectedTask.createdAt).toLocaleDateString() : 'Unknown'}
                          </span>
                        </div>
                      </div>

                      <div className="flex gap-2 pt-2">
                        <Button
                          size="sm"
                          onClick={() => {
                            updateTask(selectedTask.id, {
                              status: selectedTask.status === 'done' ? 'todo' : 'done'
                            });
                            setSelectedTask({...selectedTask, status: selectedTask.status === 'done' ? 'todo' : 'done'});
                          }}
                        >
                          {selectedTask.status === 'done' ? 'Mark Incomplete' : 'Mark Complete'}
                        </Button>
                        <Button variant="outline" size="sm">
                          <Settings className="h-3 w-3 mr-1" />
                          Edit Task
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="subtasks" className="space-y-4">
                <SubtaskManager
                  taskId={selectedTask.id}
                  taskTitle={selectedTask.title}
                  onProgressUpdate={(progress) => {
                    // Update task progress based on subtasks
                    console.log(`Task progress: ${progress}%`);
                  }}
                />
              </TabsContent>

              <TabsContent value="dependencies" className="space-y-4">
                <TaskDependencyManager
                  task={selectedTask}
                  allTasks={[
                    ...state.unassignedTasks,
                    ...state.pages.flatMap(page => page.tasks)
                  ]}
                  onUpdateTask={updateTask}
                />
              </TabsContent>

              <TabsContent value="time" className="space-y-4">
                <TimeTracker
                  taskId={selectedTask.id}
                  taskTitle={selectedTask.title}
                />
              </TabsContent>

              <TabsContent value="comments" className="space-y-4">
                <TaskComments
                  taskId={selectedTask.id}
                  taskTitle={selectedTask.title}
                />
              </TabsContent>
            </Tabs>
          )}

          {selectedTask && !advancedStats.databaseSetup && (
            <div className="text-center py-8">
              <div className="w-16 h-16 bg-orange-100 dark:bg-orange-900 rounded-full mx-auto mb-4 flex items-center justify-center">
                <Settings className="h-8 w-8 text-orange-600" />
              </div>
              <h3 className="text-lg font-semibold mb-2">Advanced Features Not Set Up</h3>
              <p className="text-muted-foreground mb-4">
                Run the database schemas to enable subtasks, dependencies, time tracking, and comments.
              </p>
              <Button onClick={() => {
                setShowTaskDetails(false);
                // Navigate to setup guide
                window.open('/profile', '_blank');
              }}>
                View Setup Guide
              </Button>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </ModernLayout>
  );
};

export default Tasker;
