import React, { useState } from 'react';
import { useTask } from '../contexts/TaskContext';
import { handleDragOver, handleDragEnter, handleDragLeave, handleDrop } from '../utils/dragDrop';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Search, Plus, Inbox, BarChart3, CheckCircle2, Globe } from 'lucide-react';
import TaskCard from '../components/TaskCard';
import PageCard from '../components/PageCard';
import AddTaskModal from '../components/AddTaskModal';
import AddPageModal from '../components/AddPageModal';
import AsyncErrorBoundary from '../components/AsyncErrorBoundary';
import ModernLayout from '../components/ModernLayout';
import { Link } from 'react-router-dom';

const Tasker: React.FC = () => {
  const { state, moveTask, searchTasks } = useTask();
  const [searchQuery, setSearchQuery] = useState('');
  const [showAddPageModal, setShowAddPageModal] = useState(false);
  const [showAddTaskModal, setShowAddTaskModal] = useState(false);

  if (state.pages.length === 0) {
    return (
      <ModernLayout>
        <div className="max-w-4xl mx-auto">
          <Card className="card-modern p-8 sm:p-12 text-center">
            <div className="max-w-md mx-auto space-y-6">
              <div className="w-20 h-20 bg-gradient-to-r from-orange-500 to-orange-600 rounded-2xl mx-auto flex items-center justify-center">
                <Globe className="w-10 h-10 text-white" />
              </div>
              <h2 className="text-2xl sm:text-3xl font-bold text-foreground">
                No Websites Yet
              </h2>
              <p className="text-muted-foreground text-base">
                You need to add at least one website or project before you can start managing tasks.
                Each website will have its own dedicated task board.
              </p>
              <div className="space-y-3">
                <Link to="/websites">
                  <Button
                    size="lg"
                    className="btn-orange w-full"
                  >
                    <Plus className="w-5 h-5 mr-2" />
                    Add Your First Website
                  </Button>
                </Link>
                <p className="text-sm text-muted-foreground">
                  Once you add a website, you'll be able to create and manage tasks for it.
                </p>
              </div>
            </div>
          </Card>
        </div>
      </ModernLayout>
    );
  }

  const filteredTasks = searchQuery.trim() 
    ? searchTasks(searchQuery)
    : state.unassignedTasks;

  const handleUnassignedDrop = (event: React.DragEvent) => {
    const dragData = handleDrop(event);
    if (dragData && dragData.type === 'task') {
      moveTask(dragData.taskId, undefined);
    }
  };

  const totalTasks = state.unassignedTasks.length + 
    state.pages.reduce((total, page) => total + page.tasks.length, 0);

  const completedTasks = state.unassignedTasks.filter(task => task.status === 'done').length +
    state.pages.reduce((total, page) => 
      total + page.tasks.filter(task => task.status === 'done').length, 0
    );

  const completionRate = totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0;

  return (
    <AsyncErrorBoundary>
      <ModernLayout>
        <div className="space-y-6">
          {/* Header */}
          <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-4">
            <div className="space-y-2">
              <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-foreground">
                Task Dashboard
              </h1>
              <p className="text-muted-foreground text-sm sm:text-base">
                Organize your work with drag-and-drop simplicity
              </p>
            </div>
            <div className="flex gap-2">
              <Link to="/websites">
                <Button variant="outline" size="lg">
                  <Globe className="w-4 h-4 mr-2" />
                  Manage Websites
                </Button>
              </Link>
              <Button
                onClick={() => setShowAddPageModal(true)}
                className="btn-orange"
                size="lg"
              >
                <Plus className="w-4 h-4 mr-2" />
                Add Website
              </Button>
            </div>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-2 sm:grid-cols-4 gap-3 sm:gap-4">
            <Card className="card-modern p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                  <BarChart3 className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                  <p className="text-xs sm:text-sm text-muted-foreground">Total Tasks</p>
                  <p className="text-lg sm:text-xl font-bold text-foreground">{totalTasks}</p>
                </div>
              </div>
            </Card>

            <Card className="card-modern p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
                  <CheckCircle2 className="w-4 h-4 text-green-600 dark:text-green-400" />
                </div>
                <div>
                  <p className="text-xs sm:text-sm text-muted-foreground">Completed</p>
                  <p className="text-lg sm:text-xl font-bold text-green-600 dark:text-green-400">{completedTasks}</p>
                </div>
              </div>
            </Card>

            <Card className="card-modern p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-purple-100 dark:bg-purple-900 rounded-lg">
                  <Inbox className="w-4 h-4 text-purple-600 dark:text-purple-400" />
                </div>
                <div>
                  <p className="text-xs sm:text-sm text-muted-foreground">Pages</p>
                  <p className="text-lg sm:text-xl font-bold text-purple-600 dark:text-purple-400">{state.pages.length}</p>
                </div>
              </div>
            </Card>

            <Card className="card-modern p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-orange-100 dark:bg-orange-900 rounded-lg">
                  <div className="w-4 h-4 text-orange-600 dark:text-orange-400 font-bold text-xs flex items-center justify-center">
                    %
                  </div>
                </div>
                <div>
                  <p className="text-xs sm:text-sm text-muted-foreground">Progress</p>
                  <p className="text-lg sm:text-xl font-bold text-orange-600 dark:text-orange-400">{completionRate}%</p>
                  </div>
                </div>
              </Card>
            </div>

          <div className="grid lg:grid-cols-4 gap-4 lg:gap-6">
            {/* Sidebar - Unassigned Tasks */}
            <div className="lg:col-span-1 order-2 lg:order-1">
              <Card className="card-modern sticky top-4">
                <CardHeader className="pb-3">
                  <div className="flex items-center gap-2 mb-3">
                    <div className="p-2 bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg">
                      <Inbox className="w-4 h-4 text-white" />
                    </div>
                    <div>
                      <h2 className="font-semibold text-base sm:text-lg text-foreground">Tasks to Assign</h2>
                      <p className="text-xs text-muted-foreground">{filteredTasks.length} tasks</p>
                    </div>
                  </div>
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                    <Input
                      placeholder="Search tasks..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </CardHeader>
              
              <CardContent 
                className="max-h-[50vh] lg:max-h-96 overflow-y-auto space-y-1"
                onDragOver={handleDragOver}
                onDragEnter={handleDragEnter}
                onDragLeave={handleDragLeave}
                onDrop={handleUnassignedDrop}
              >
                {/* Add Task Button */}
                <Button
                  onClick={() => setShowAddTaskModal(true)}
                  className="w-full justify-start text-left btn-orange mb-4"
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Add New Task
                </Button>

                {filteredTasks.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    {searchQuery ? (
                      <div className="space-y-2">
                        <Search className="w-8 h-8 mx-auto opacity-50" />
                        <p className="text-sm">No tasks found for "{searchQuery}"</p>
                      </div>
                    ) : (
                      <div className="space-y-2">
                        <Inbox className="w-8 h-8 mx-auto opacity-50" />
                        <p className="text-sm">No unassigned tasks</p>
                        <p className="text-xs">Click "Add New Task" to get started</p>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="space-y-2">
                    {filteredTasks.map((task, index) => (
                      <TaskCard
                        key={task.id}
                        task={task}
                        index={index}
                        showFullDetails={true}
                      />
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Main Content - Pages */}
          <div className="lg:col-span-3 order-1 lg:order-2">
            {state.pages.length === 0 ? (
              <Card className="card-modern p-8 sm:p-12 text-center">
                <div className="max-w-md mx-auto space-y-4">
                  <div className="w-16 h-16 bg-gradient-to-r from-orange-500 to-orange-600 rounded-2xl mx-auto flex items-center justify-center">
                    <Plus className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-xl sm:text-2xl font-semibold text-foreground">
                    No Pages Yet
                  </h3>
                  <p className="text-muted-foreground text-sm sm:text-base">
                    Create your first page to start organizing your tasks.
                    Pages help you group related tasks together by project or category.
                  </p>
                  <Button
                    onClick={() => setShowAddPageModal(true)}
                    className="btn-orange"
                    size="lg"
                  >
                    <Plus className="w-4 h-4 mr-2" />
                    Create Your First Page
                  </Button>
                </div>
              </Card>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4 sm:gap-6">
                {state.pages.map((page) => (
                  <div key={page.id} className="animate-fade-in">
                    <PageCard page={page} />
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        <AddPageModal
          isOpen={showAddPageModal}
          onClose={() => setShowAddPageModal(false)}
        />

        <AddTaskModal
          isOpen={showAddTaskModal}
          onClose={() => setShowAddTaskModal(false)}
        />
      </ModernLayout>
    </AsyncErrorBoundary>
  );
};

export default Tasker;
