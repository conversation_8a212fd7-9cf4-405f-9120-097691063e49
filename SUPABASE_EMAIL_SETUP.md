# 🔐 Supabase Email Authentication Setup Guide

## 📧 Configure Password Reset Emails

### Step 1: Access Supabase Dashboard

1. **Login** to your Supabase dashboard: https://app.supabase.com
2. **Select** your EasTask project
3. **Navigate** to Authentication → Settings → Email Templates

### Step 2: Configure Email Templates

#### **Password Recovery Template**

1. **Click** on "Password Recovery" template
2. **Replace** the default template with this custom one:

```html
<h2>Reset Your EasTask Password</h2>

<p>Hi there,</p>

<p>We received a request to reset your password for your EasTask account. Click the button below to create a new password:</p>

<p><a href="{{ .SiteURL }}/reset-password?access_token={{ .TokenHash }}&type=recovery&refresh_token={{ .RefreshTokenHash }}">Reset Password</a></p>

<p>This link will expire in {{ .ExpiresIn }} for security reasons.</p>

<p>If you didn't request a password reset, you can safely ignore this email. Your password will not be changed unless you click the link above and create a new one.</p>

<p>Best regards,<br>
The EasTask Team</p>
```

3. **Update** the subject line to: `Reset your EasTask password`

#### **Email Confirmation Template**

1. **Click** on "Confirm signup" template
2. **Replace** with this template:

```html
<h2>Welcome to EasTask!</h2>

<p>Hi there,</p>

<p>Thanks for signing up for EasTask! Please confirm your email address by clicking the button below:</p>

<p><a href="{{ .ConfirmationURL }}">Confirm Email Address</a></p>

<p>If you didn't create an account with EasTask, you can safely ignore this email.</p>

<p>Welcome to professional task management!</p>

<p>Best regards,<br>
The EasTask Team</p>
```

3. **Update** the subject line to: `Welcome to EasTask - Confirm your email`

### Step 3: Configure SMTP Settings (Optional)

For better email deliverability, you can configure custom SMTP:

1. **Go to** Authentication → Settings → SMTP Settings
2. **Enable** "Enable custom SMTP"
3. **Configure** with your email provider:

#### **Using Resend SMTP:**
- **Host**: `smtp.resend.com`
- **Port**: `587`
- **Username**: `resend`
- **Password**: Your Resend API key
- **Sender email**: `<EMAIL>` (or your verified domain)
- **Sender name**: `EasTask Team`

#### **Using Gmail SMTP:**
- **Host**: `smtp.gmail.com`
- **Port**: `587`
- **Username**: Your Gmail address
- **Password**: App-specific password
- **Sender email**: Your Gmail address
- **Sender name**: `EasTask Team`

### Step 4: Configure Site URL

1. **Go to** Authentication → Settings → General
2. **Set Site URL** to your domain:
   - **Development**: `http://localhost:8081`
   - **Production**: `https://yourdomain.com`

### Step 5: Configure Redirect URLs

1. **Add** these redirect URLs:
   - `http://localhost:8081/reset-password`
   - `https://yourdomain.com/reset-password` (for production)

### Step 6: Test Email Functionality

1. **Go to** your EasTask application
2. **Click** "Forgot Password?" on login page
3. **Enter** your email address
4. **Check** your inbox for the reset email
5. **Click** the reset link
6. **Set** a new password

---

## 🎨 Custom Email Styling (Advanced)

### Enhanced Password Recovery Template

```html
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Your EasTask Password</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background: linear-gradient(135deg, #f97316 0%, #ea580c 100%); padding: 40px 20px; text-align: center; border-radius: 10px 10px 0 0;">
        <h1 style="color: white; margin: 0; font-size: 28px;">EasTask</h1>
        <p style="color: white; margin: 10px 0 0 0; opacity: 0.9;">Professional Task Management</p>
    </div>
    
    <div style="background: #ffffff; padding: 40px 20px; border: 1px solid #e5e7eb; border-top: none;">
        <h2 style="color: #1f2937; margin: 0 0 20px 0;">Reset Your Password</h2>
        
        <p style="color: #4b5563; margin: 0 0 20px 0;">
            We received a request to reset your password for your EasTask account. Click the button below to create a new password:
        </p>
        
        <div style="text-align: center; margin: 30px 0;">
            <a href="{{ .SiteURL }}/reset-password?access_token={{ .TokenHash }}&type=recovery&refresh_token={{ .RefreshTokenHash }}" 
               style="background: linear-gradient(135deg, #f97316 0%, #ea580c 100%); color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-weight: bold; display: inline-block;">
                Reset Password
            </a>
        </div>
        
        <p style="color: #6b7280; font-size: 14px; margin: 20px 0 0 0;">
            This link will expire in {{ .ExpiresIn }} for security reasons.
        </p>
        
        <p style="color: #6b7280; font-size: 14px; margin: 10px 0 0 0;">
            If you can't click the button, copy and paste this link into your browser:
        </p>
        
        <p style="color: #6b7280; font-size: 14px; margin: 5px 0 0 0; word-break: break-all;">
            {{ .SiteURL }}/reset-password?access_token={{ .TokenHash }}&type=recovery&refresh_token={{ .RefreshTokenHash }}
        </p>
    </div>
    
    <div style="background: #f9fafb; padding: 20px; text-align: center; color: #6b7280; font-size: 12px; border-radius: 0 0 10px 10px;">
        <p style="margin: 0 0 10px 0;">If you didn't request a password reset, you can safely ignore this email.</p>
        <p style="margin: 0;">Your password will not be changed unless you click the link above and create a new one.</p>
    </div>
</body>
</html>
```

---

## 🔧 Troubleshooting

### Email Not Received
1. **Check spam folder**
2. **Verify email address** is correct
3. **Check Supabase logs** in Dashboard → Logs
4. **Verify SMTP settings** if using custom SMTP

### Reset Link Not Working
1. **Check URL parameters** are complete
2. **Verify Site URL** is configured correctly
3. **Check link expiration** (default: 1 hour)
4. **Try requesting new reset link**

### Custom SMTP Issues
1. **Verify credentials** are correct
2. **Check firewall settings**
3. **Test with default Supabase SMTP** first
4. **Check provider-specific settings**

---

## 🚀 Production Checklist

- [ ] **Custom email templates** configured
- [ ] **Site URL** set to production domain
- [ ] **Redirect URLs** include production URLs
- [ ] **SMTP settings** configured (optional)
- [ ] **Email deliverability** tested
- [ ] **Password reset flow** tested end-to-end
- [ ] **Email confirmation** tested for new signups

---

**🎉 Your password reset system is now fully configured and ready for production!**
